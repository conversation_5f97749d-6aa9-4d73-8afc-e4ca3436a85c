from sqlalchemy import Column, Integer, String, Float, Text, DateTime, Boolean, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    phone = Column(String)
    is_active = Column(Boolean, default=True)
    is_agent = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Property(Base):
    __tablename__ = "properties"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    description = Column(Text)
    address = Column(String, nullable=False)
    city = Column(String, nullable=False, index=True)
    state = Column(String, nullable=False, index=True)
    zip_code = Column(String, nullable=False)
    latitude = Column(Float)
    longitude = Column(Float)
    
    # Property details
    price = Column(Integer, nullable=False, index=True)
    bedrooms = Column(Integer, nullable=False, index=True)
    bathrooms = Column(Float, nullable=False, index=True)
    square_feet = Column(Integer)
    lot_size = Column(Float)
    year_built = Column(Integer)
    property_type = Column(String, nullable=False, index=True)  # house, condo, townhouse, etc.
    listing_type = Column(String, nullable=False, default="for_sale")  # for_sale, for_rent
    
    # Status and dates
    status = Column(String, default="active")  # active, pending, sold
    listed_date = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Agent/Owner info
    agent_id = Column(Integer, ForeignKey("users.id"))
    agent = relationship("User", back_populates="properties")
    
    # Relationships
    images = relationship("PropertyImage", back_populates="property", cascade="all, delete-orphan")

# Add back_populates to User model
User.properties = relationship("Property", back_populates="agent")

class PropertyImage(Base):
    __tablename__ = "property_images"
    
    id = Column(Integer, primary_key=True, index=True)
    property_id = Column(Integer, ForeignKey("properties.id"), nullable=False)
    image_url = Column(String, nullable=False)
    alt_text = Column(String)
    is_primary = Column(Boolean, default=False)
    order = Column(Integer, default=0)
    
    property = relationship("Property", back_populates="images")
