from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import List, Optional
import models
import schemas
from auth import get_password_hash

# User CRUD operations
def get_user(db: Session, user_id: int):
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_email(db: Session, email: str):
    return db.query(models.User).filter(models.User.email == email).first()

def get_user_by_username(db: Session, username: str):
    return db.query(models.User).filter(models.User.username == username).first()

def create_user(db: Session, user: schemas.UserCreate):
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        username=user.username,
        hashed_password=hashed_password,
        first_name=user.first_name,
        last_name=user.last_name,
        phone=user.phone,
        is_agent=user.is_agent
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

# Property CRUD operations
def get_property(db: Session, property_id: int):
    return db.query(models.Property).filter(models.Property.id == property_id).first()

def get_properties(db: Session, skip: int = 0, limit: int = 20):
    return db.query(models.Property).filter(models.Property.status == "active").offset(skip).limit(limit).all()

def create_property(db: Session, property: schemas.PropertyCreate, agent_id: int):
    db_property = models.Property(**property.dict(), agent_id=agent_id)
    db.add(db_property)
    db.commit()
    db.refresh(db_property)
    return db_property

def search_properties(db: Session, search: schemas.PropertySearch):
    query = db.query(models.Property).filter(models.Property.status == "active")
    
    # Location filter
    if search.location:
        location_filter = or_(
            models.Property.city.ilike(f"%{search.location}%"),
            models.Property.state.ilike(f"%{search.location}%"),
            models.Property.address.ilike(f"%{search.location}%"),
            models.Property.zip_code.ilike(f"%{search.location}%")
        )
        query = query.filter(location_filter)
    
    # Price filters
    if search.min_price:
        query = query.filter(models.Property.price >= search.min_price)
    if search.max_price:
        query = query.filter(models.Property.price <= search.max_price)
    
    # Property details filters
    if search.bedrooms:
        query = query.filter(models.Property.bedrooms >= search.bedrooms)
    if search.bathrooms:
        query = query.filter(models.Property.bathrooms >= search.bathrooms)
    if search.property_type:
        query = query.filter(models.Property.property_type == search.property_type)
    if search.listing_type:
        query = query.filter(models.Property.listing_type == search.listing_type)
    
    # Sorting
    if search.sort_by == "price_asc":
        query = query.order_by(models.Property.price.asc())
    elif search.sort_by == "price_desc":
        query = query.order_by(models.Property.price.desc())
    elif search.sort_by == "date_desc":
        query = query.order_by(models.Property.listed_date.desc())
    else:
        query = query.order_by(models.Property.price.asc())
    
    # Pagination
    skip = (search.page - 1) * search.limit
    properties = query.offset(skip).limit(search.limit).all()
    total = query.count()
    
    return properties, total

# Property Image CRUD operations
def create_property_image(db: Session, image: schemas.PropertyImageCreate, property_id: int):
    db_image = models.PropertyImage(**image.dict(), property_id=property_id)
    db.add(db_image)
    db.commit()
    db.refresh(db_image)
    return db_image
