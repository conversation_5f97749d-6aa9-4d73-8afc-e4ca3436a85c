{% extends "base.html" %}

{% block title %}Real Home - Find Your Dream Home{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-main-title">Agents. Tours.</h1>
                <h1 class="hero-main-title">Loans. Homes.</h1>
            </div>
            <div class="hero-search">
                <form action="/search" method="GET" class="search-form">
                    <div class="search-input-container">
                        <input type="text"
                               class="search-input"
                               id="location"
                               name="location"
                               placeholder="Enter an address, neighborhood, city, or ZIP code">
                        <button type="submit" class="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- BuyAbility Section -->
<section class="buyability-section">
    <div class="container">
        <h3 class="buyability-section-title">Find homes you can afford with BuyAbility<sup>SM</sup></h3>
        <p class="buyability-section-subtitle">Answer a few questions. We'll highlight homes you're likely to qualify for.</p>

        <div class="buyability-layout">
            <!-- Fixed Calculator Card -->
            <div class="buyability-calculator-fixed">
                <div class="buyability-calculator">
                    <div class="zillow-home-loans-logo">
                        <i class="fas fa-home text-primary"></i>
                        <span class="fw-bold text-primary">Zillow Home Loans</span>
                    </div>

                    <form class="buyability-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>Suggested target price</label>
                                <div class="price-input">
                                    <span class="currency">$</span>
                                    <input type="text" class="form-control" placeholder="500,000" value="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>BuyAbility<sup>SM</sup></label>
                                <div class="price-input">
                                    <span class="currency">$</span>
                                    <input type="text" class="form-control" placeholder="450,000" value="">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Mo. payment</label>
                                <div class="price-input">
                                    <span class="currency">$</span>
                                    <input type="text" class="form-control" placeholder="2,500" value="">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Today's rate</label>
                                <div class="rate-input">
                                    <input type="text" class="form-control" placeholder="6.75" value="">
                                    <span class="percentage">%</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>APR</label>
                                <div class="rate-input">
                                    <input type="text" class="form-control" placeholder="6.89" value="">
                                    <span class="percentage">%</span>
                                </div>
                            </div>
                        </div>

                        <button type="button" class="btn btn-primary w-100 mt-2">Let's get started</button>
                    </form>
                </div>
            </div>

            <!-- Scrollable Properties -->
            <div class="buyability-properties-scroll">
                <div class="buyability-scroll-container">
                    <div class="buyability-scroll-wrapper">
                        {% if featured_properties %}
                        {% for property in featured_properties %}
                        <div class="buyability-property-item">
                            <div class="card buyability-property-card">
                                <div class="position-relative">
                                    {% if property.images %}
                                        <img src="{{ property.images[0].image_url }}" class="card-img-top buyability-property-image"
                                             alt="{{ property.title }}">
                                    {% else %}
                                        <div class="card-img-top buyability-property-image bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-home fa-2x text-muted"></i>
                                        </div>
                                    {% endif %}
                                    <div class="buyability-badge">
                                        <span class="badge bg-warning text-dark">Within BuyAbility</span>
                                    </div>
                                </div>

                                <div class="card-body p-3">
                                    <div class="property-price fw-bold mb-2">${{ "{:,}".format(property.price) }}</div>
                                    <div class="property-details-small">
                                        <span>{{ property.bedrooms }} bd</span>
                                        <span>{{ property.bathrooms }} ba</span>
                                        {% if property.square_feet %}
                                        <span>{{ "{:,}".format(property.square_feet) }} sqft</span>
                                        {% endif %}
                                    </div>
                                    <div class="property-address text-muted small">
                                        {{ property.city }}, {{ property.state }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="text-center p-4">
                            <p class="text-muted">No properties available at the moment.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
