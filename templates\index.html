{% extends "base.html" %}

{% block title %}Real Home - Find Your Dream Home{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-main-title">Agents. Tours.</h1>
                <h1 class="hero-main-title">Loans. Homes.</h1>
            </div>
            <div class="hero-search">
                <form action="/search" method="GET" class="search-form">
                    <div class="search-input-container">
                        <input type="text"
                               class="search-input"
                               id="location"
                               name="location"
                               placeholder="Enter an address, neighborhood, city, or ZIP code">
                        <button type="submit" class="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties -->
<section class="featured-properties">
    <div class="container">
        <h2 class="text-center">Featured Properties</h2>
        
        {% if featured_properties %}
        <div class="row g-4">
            {% for property in featured_properties %}
            <div class="col-lg-4 col-md-6">
                <div class="card property-card shadow-sm">
                    <div class="position-relative">
                        {% if property.images %}
                            <img src="{{ property.images[0].image_url }}" class="card-img-top property-image" 
                                 alt="{{ property.title }}">
                        {% else %}
                            <div class="card-img-top property-image bg-light d-flex align-items-center justify-content-center">
                                <i class="fas fa-home fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge bg-primary fs-6">${{ "{:,}".format(property.price) }}</span>
                        </div>
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ property.title }}</h5>
                        <p class="card-text text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ property.city }}, {{ property.state }}
                        </p>

                        <div class="property-details mb-3">
                            <span>
                                <i class="fas fa-bed"></i>{{ property.bedrooms }}
                            </span>
                            <span>
                                <i class="fas fa-bath"></i>{{ property.bathrooms }}
                            </span>
                            {% if property.square_feet %}
                            <span>
                                <i class="fas fa-ruler-combined"></i>{{ "{:,}".format(property.square_feet) }} sqft
                            </span>
                            {% endif %}
                        </div>

                        <div class="mt-auto">
                            <a href="/property/{{ property.id }}" class="btn btn-outline-primary w-100">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-4">
            <a href="/search" class="btn btn-primary btn-lg">View All Properties</a>
        </div>
        {% else %}
        <div class="text-center">
            <p class="text-muted">No properties available at the moment.</p>
            <a href="/search" class="btn btn-primary">Browse Properties</a>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}
