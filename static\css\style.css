/* Custom CSS for Real Home - Zillow Clone */

:root {
    --primary-color: #006ba6;
    --secondary-color: #0496c7;
    --accent-color: #9bb1ff;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --border-color: #e9ecef;
    --hover-color: #f8f9fa;
}

html, body {
    width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Navigation - Zillow-style Layout */
.navbar {
    font-family: Inter, "Adjusted Arial", Tahoma, Geneva, sans-serif !important;
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    height: 64px !important;
    z-index: 1000;
    background-color: white !important;
    width: 100%;
    border-bottom: 1px solid #e6e6e6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    padding: 0 !important;
}

.navbar .container-fluid {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px !important;
    position: relative;
    flex-wrap: nowrap !important;
}

@media (min-width: 890px) {
    .navbar .container-fluid {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
    }

    .navbar-left {
        display: flex !important;
        align-items: center;
        flex: 0 0 auto;
    }

    .navbar-brand {
        position: absolute !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }

    .navbar-right {
        display: flex !important;
        align-items: center;
        flex: 0 0 auto;
    }
}

/* Left Navigation */
.navbar-left {
    display: flex !important;
    align-items: center;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    flex-direction: row !important;
    gap: 0 !important;
}

/* Right Navigation */
.navbar-right {
    display: flex !important;
    align-items: center;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    flex-direction: row !important;
    gap: 0 !important;
}

/* Center Brand */
.navbar-brand {
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    font-size: 1.6rem !important;
    font-weight: 700 !important;
    color: #006ba6 !important;
    text-decoration: none !important;
    transition: color 0.3s ease;
    display: flex !important;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    z-index: 10;
    margin: 0 !important;
    padding: 0 !important;
    height: auto !important;
}

.navbar-brand:hover {
    color: #0496c7 !important;
    text-decoration: none !important;
}

.brand-text {
    font-weight: 700 !important;
    white-space: nowrap;
    color: #006ba6 !important;
    font-size: 1.6rem !important;
}

/* Navigation Items */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.navbar-nav .nav-item {
    display: flex !important;
    align-items: center;
    margin: 0 !important;
    padding: 0 !important;
}

.navbar-nav .nav-link {
    font-weight: 400 !important;
    color: #333 !important;
    transition: color 0.2s ease;
    padding: 0 14px !important;
    font-size: 16px !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    height: 64px !important;
    border: none !important;
    background: none !important;
    border-radius: 0 !important;
    font-family: Inter, "Adjusted Arial", Tahoma, Geneva, sans-serif !important;
    margin: 0 !important;
    line-height: 1 !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color) !important;
    background: none !important;
    text-decoration: none !important;
}

/* Desktop Navigation Display */
@media (min-width: 890px) {
    .navbar-left,
    .navbar-right {
        display: flex !important;
        visibility: visible !important;
    }

    /* Hide mobile menu on desktop */
    .navbar-collapse {
        display: none !important;
        visibility: hidden !important;
    }

    .navbar-toggler {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Mobile Navigation Display */
@media (max-width: 889px) {
    .navbar-left,
    .navbar-right {
        display: none !important;
        visibility: hidden !important;
    }

    .navbar-collapse {
        display: none;
        visibility: hidden;
    }

    .navbar-collapse.show {
        display: block !important;
        visibility: visible !important;
    }

    .navbar-toggler {
        display: block !important;
        visibility: visible !important;
    }
}

/* Mobile Toggle Button */
.navbar-toggler {
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 20px;
    height: 20px;
}

/* Main content padding for fixed navbar */
.main-content {
    padding-top: 64px;
}

/* Ensure body doesn't have margin/padding conflicts */
body {
    margin: 0;
    padding: 0;
}

/* Navbar scroll behavior */
.navbar.scrolled {
    height: 56px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.navbar.scrolled .nav-link {
    height: 56px;
}

/* Active nav link styling */
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 1px;
}

/* Override Bootstrap navbar styles */
.navbar {
    --bs-navbar-padding-x: 0 !important;
    --bs-navbar-padding-y: 0 !important;
    --bs-navbar-color: #333 !important;
    --bs-navbar-hover-color: #006ba6 !important;
    --bs-navbar-brand-padding-y: 0 !important;
    --bs-navbar-brand-margin-end: 0 !important;
    --bs-navbar-nav-link-padding-x: 16px !important;
}

.navbar-nav {
    --bs-nav-link-padding-x: 16px !important;
    --bs-nav-link-padding-y: 0 !important;
    --bs-nav-link-color: #333 !important;
    --bs-nav-link-hover-color: #006ba6 !important;
}

/* Professional box-sizing for all elements */
*, ::after, ::before {
    box-sizing: border-box;
}

/* Mobile Menu Styles */
@media (max-width: 889px) {
    .navbar-collapse {
        position: fixed;
        top: 64px;
        left: 0;
        width: 100%;
        height: calc(100vh - 64px);
        background: white;
        border-top: 1px solid #e6e6e6;
        z-index: 999;
        padding: 20px 24px;
        overflow-y: auto;
    }

    .navbar-collapse .navbar-nav {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
    }

    .navbar-collapse .nav-link {
        height: auto !important;
        padding: 16px 0 !important;
        border-bottom: 1px solid #f0f0f0;
        font-size: 16px;
    }

    .navbar-collapse .nav-link:last-child {
        border-bottom: none;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    /* Use online image (current) */
    background-image: url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    /* To use local image, uncomment the line below and comment the line above */
    /* background-image: url('/static/images/hero-bg.jpg'); */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    width: 100%;
    padding: 0 20px;
}

.hero-text {
    margin-bottom: 3rem;
}

.hero-main-title {
    font-size: 4.5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-search {
    display: flex;
    justify-content: center;
}

.search-form {
    width: 100%;
    max-width: 550px;
}

.search-input-container {
    position: relative;
    display: flex;
    background: white;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 44px;
    border: 1px solid #e6e6e6;
}

.search-input {
    flex: 1;
    border: none;
    padding: 10px 16px;
    font-size: 15px;
    outline: none;
    background: transparent;
    color: #333;
    height: 42px;
    font-family: Inter, "Adjusted Arial", Tahoma, Geneva, sans-serif;
}

.search-input::placeholder {
    color: #888;
    font-size: 15px;
}

.search-input:focus {
    outline: none;
    box-shadow: none;
}

.search-input-container:focus-within {
    border-color: #006ba6;
    box-shadow: 0 0 0 2px rgba(0, 107, 166, 0.1);
}

.search-button {
    background: var(--primary-color);
    border: none;
    padding: 10px 18px;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 42px;
    min-width: 44px;
}

.search-button:hover {
    background: var(--secondary-color);
}

.search-button i {
    font-size: 18px;
}

/* Property Cards */
.property-card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    height: 100%;
}

.property-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border-color: var(--primary-color);
}

.property-image {
    height: 220px;
    object-fit: cover;
    width: 100%;
    background-color: var(--hover-color);
}

.property-details {
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
}

.property-details span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.property-details i {
    color: var(--primary-color);
    font-size: 0.8rem;
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    color: var(--text-dark);
}

/* BuyAbility Section */
.buyability-section {
    padding: 60px 0;
    background-color: #f8f9fa;
}

.buyability-section-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.3;
    text-align: center;
}

.buyability-section-subtitle {
    color: #666;
    font-size: 16px;
    margin-bottom: 40px;
    line-height: 1.4;
    text-align: center;
}

/* BuyAbility Layout */
.buyability-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.buyability-calculator-fixed {
    flex: 0 0 320px;
    min-width: 320px;
}

.buyability-properties-scroll {
    flex: 1;
    min-width: 0;
}

.buyability-calculator-item {
    flex: 0 0 320px;
    min-width: 320px;
}

.buyability-calculator {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 100%;
    border: 1px solid #e6e6e6;
    width: 100%;
}

.zillow-home-loans-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.buyability-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.3;
}

.buyability-subtitle {
    color: #666;
    font-size: 14px;
    margin-bottom: 25px;
    line-height: 1.4;
}

.buyability-form .form-row {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.buyability-form .form-row:last-of-type {
    margin-bottom: 16px;
}

.buyability-form .form-group {
    flex: 1;
    min-width: 0;
}

.buyability-form label {
    display: block;
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.price-input, .rate-input {
    position: relative;
    display: flex;
    align-items: center;
}

.price-input .currency {
    position: absolute;
    left: 12px;
    color: #333;
    font-weight: 500;
    z-index: 2;
}

.rate-input .percentage {
    position: absolute;
    right: 12px;
    color: #333;
    font-weight: 500;
    z-index: 2;
}

.buyability-form .form-control {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 10px;
    font-size: 13px;
    height: 36px;
    font-weight: 500;
}

.price-input .form-control {
    padding-left: 25px;
}

.rate-input .form-control {
    padding-right: 25px;
}

.buyability-form .form-control:focus {
    border-color: #006ba6;
    box-shadow: 0 0 0 2px rgba(0, 107, 166, 0.1);
    outline: none;
}

.buyability-properties {
    padding-left: 30px;
}

.buyability-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
    width: 100%;
}

.buyability-scroll-container::-webkit-scrollbar {
    height: 6px;
}

.buyability-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.buyability-scroll-container::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

.buyability-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #999;
}

.buyability-scroll-wrapper {
    display: flex;
    gap: 16px;
    padding-bottom: 10px;
    width: max-content;
}

.buyability-property-item {
    flex: 0 0 280px;
    min-width: 280px;
}

.buyability-property-card {
    border: none;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease;
    height: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.buyability-property-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.buyability-property-image {
    height: 160px;
    object-fit: cover;
    width: 100%;
}

.buyability-badge {
    position: absolute;
    top: 10px;
    left: 10px;
}

.buyability-badge .badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.buyability-property-card .property-price {
    font-size: 16px;
    color: #333;
}

.property-details-small {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.property-details-small span {
    white-space: nowrap;
}

.property-address {
    font-size: 12px;
    color: #888;
}

/* Search Page */
.search-filters {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section {
    margin-bottom: 1rem;
}

.filter-section:last-child {
    margin-bottom: 0;
}

/* Property Detail Page */
.property-gallery {
    border-radius: 12px;
    overflow: hidden;
}

.property-main-image {
    height: 400px;
    object-fit: cover;
    width: 100%;
}

.property-thumbnail {
    height: 80px;
    object-fit: cover;
    width: 100%;
    cursor: pointer;
    border-radius: 8px;
    transition: opacity 0.3s ease;
}

.property-thumbnail:hover {
    opacity: 0.8;
}

.property-info-card {
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.property-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.property-features {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--hover-color);
    border-radius: 20px;
    font-size: 0.9rem;
}

.feature-item i {
    color: var(--primary-color);
}

/* Map */
.map-container {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

/* Forms */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 107, 166, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
    padding: 0.75rem 1rem;
}

.pagination .page-link:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Layout Improvements */
.container {
    max-width: 1200px;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

/* Main content wrapper */
main {
    width: 100%;
    overflow-x: hidden;
}

/* Ensure sections don't overflow */
section {
    width: 100%;
    overflow-x: hidden;
}

/* Featured Properties Section */
.featured-properties {
    padding: 4rem 0;
}

.featured-properties h2 {
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 3rem;
}

/* Price Badge */
.badge {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
}

/* Address styling */
.card-text {
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Responsive Design */
/* Large Desktop */
@media (min-width: 1200px) {
    .hero-main-title {
        font-size: 5.5rem;
    }

    .search-input-container {
        max-width: 600px;
    }
}

/* Desktop */
@media (min-width: 890px) {
    .hero-main-title {
        font-size: 5rem;
    }
}

/* Tablet */
@media (max-width: 889px) and (min-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }

    .hero-main-title {
        font-size: 3.5rem;
    }

    .hero-content {
        padding: 0 15px;
    }

    /* BuyAbility tablet styles */
    .buyability-layout {
        flex-direction: column;
        gap: 20px;
    }

    .buyability-calculator-fixed {
        flex: none;
        width: 100%;
    }

    .buyability-calculator-item {
        flex: 0 0 300px;
        min-width: 300px;
    }

    .buyability-property-item {
        flex: 0 0 260px;
        min-width: 260px;
    }
}

/* Mobile */
@media (max-width: 767px) {
    .hero-section {
        min-height: 70vh;
    }

    .hero-main-title {
        font-size: 2.5rem;
    }

    .hero-text {
        margin-bottom: 2rem;
    }

    /* BuyAbility mobile styles */
    .buyability-section-title {
        font-size: 1.4rem;
    }

    .buyability-section-subtitle {
        font-size: 14px;
        margin-bottom: 30px;
    }

    .buyability-layout {
        flex-direction: column;
        gap: 20px;
    }

    .buyability-calculator-fixed {
        flex: none;
        width: 100%;
    }

    .buyability-calculator-item {
        flex: 0 0 280px;
        min-width: 280px;
    }

    .buyability-calculator {
        padding: 16px;
    }

    .buyability-form .form-row {
        gap: 8px;
        margin-bottom: 10px;
    }

    .buyability-property-item {
        flex: 0 0 240px;
        min-width: 240px;
    }

    .buyability-scroll-wrapper {
        gap: 12px;
    }

    .search-input {
        padding: 8px 12px;
        font-size: 14px;
        height: 40px;
    }

    .search-input::placeholder {
        font-size: 14px;
    }

    .search-button {
        padding: 8px 14px;
        height: 40px;
        min-width: 40px;
    }

    .search-button i {
        font-size: 14px;
    }

    .search-input-container {
        height: 40px;
    }

    /* Mobile navbar styles */
    .navbar {
        height: 64px;
    }

    .navbar .container-fluid {
        padding-right: 15px;
        padding-left: 15px;
    }

    .navbar-brand {
        font-size: 1.3rem;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    .navbar-brand i {
        font-size: 1.1rem;
    }

    .main-content {
        padding-top: 64px;
    }

    .property-features {
        justify-content: center;
        gap: 0.5rem;
    }

    .search-filters {
        padding: 1rem;
    }

    .property-image {
        height: 200px;
    }

    .card-body {
        padding: 1rem;
    }

    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* Tablet navbar adjustments */
@media (max-width: 889px) and (min-width: 768px) {
    .navbar .container-fluid {
        padding-right: 20px;
        padding-left: 20px;
    }
}

/* Very small mobile devices */
@media (max-width: 576px) {
    .hero-section {
        min-height: 60vh;
    }

    .hero-main-title {
        font-size: 2rem;
    }

    .hero-content {
        padding: 0 10px;
    }

    .search-input {
        padding: 8px 10px;
        font-size: 13px;
        height: 36px;
    }

    .search-button {
        padding: 8px 12px;
        height: 36px;
        min-width: 36px;
    }

    .search-input-container {
        height: 36px;
    }

    /* Extra small mobile navbar */
    .navbar .container-fluid {
        padding-right: 10px;
        padding-left: 10px;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .navbar-brand i {
        font-size: 1rem;
    }

    .property-details {
        font-size: 0.8rem;
        gap: 0.5rem;
    }

    .container {
        padding-left: 5px;
        padding-right: 5px;
    }

    .col-lg-4, .col-md-6 {
        padding-left: 5px;
        padding-right: 5px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}
