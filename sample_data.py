#!/usr/bin/env python3
"""
Sample data script to populate the database with test properties and users.
Run this script after setting up the database to add sample data.
"""

from sqlalchemy.orm import Session
from database import SessionLocal, engine
import models
import crud
import schemas
from auth import get_password_hash

# Create tables
models.Base.metadata.create_all(bind=engine)

def create_sample_users(db: Session):
    """Create sample users including agents"""
    users_data = [
        {
            "email": "<EMAIL>",
            "username": "johnagent",
            "password": "password123",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "phone": "(*************",
            "is_agent": True
        },
        {
            "email": "<EMAIL>",
            "username": "sarahagent",
            "password": "password123",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "phone": "(*************",
            "is_agent": True
        },
        {
            "email": "<EMAIL>",
            "username": "mikebuyer",
            "password": "password123",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "phone": "(*************",
            "is_agent": False
        }
    ]
    
    created_users = []
    for user_data in users_data:
        # Check if user already exists
        existing_user = crud.get_user_by_email(db, user_data["email"])
        if not existing_user:
            user_create = schemas.UserCreate(**user_data)
            user = crud.create_user(db, user_create)
            created_users.append(user)
            print(f"Created user: {user.username}")
        else:
            created_users.append(existing_user)
            print(f"User already exists: {existing_user.username}")
    
    return created_users

def create_sample_properties(db: Session, agents):
    """Create sample properties"""
    properties_data = [
        {
            "title": "Beautiful Modern Family Home",
            "description": "Stunning 4-bedroom, 3-bathroom home in a quiet neighborhood. Features include hardwood floors, updated kitchen with granite countertops, spacious living areas, and a large backyard perfect for entertaining.",
            "address": "123 Oak Street",
            "city": "Austin",
            "state": "TX",
            "zip_code": "78701",
            "latitude": 30.2672,
            "longitude": -97.7431,
            "price": 450000,
            "bedrooms": 4,
            "bathrooms": 3.0,
            "square_feet": 2500,
            "lot_size": 0.25,
            "year_built": 2015,
            "property_type": "house",
            "listing_type": "for_sale"
        },
        {
            "title": "Downtown Luxury Condo",
            "description": "Luxurious 2-bedroom condo in the heart of downtown. Floor-to-ceiling windows, modern appliances, and stunning city views. Building amenities include gym, pool, and concierge service.",
            "address": "456 Main Street, Unit 1205",
            "city": "Austin",
            "state": "TX",
            "zip_code": "78701",
            "latitude": 30.2640,
            "longitude": -97.7469,
            "price": 325000,
            "bedrooms": 2,
            "bathrooms": 2.0,
            "square_feet": 1200,
            "year_built": 2020,
            "property_type": "condo",
            "listing_type": "for_sale"
        },
        {
            "title": "Charming Suburban Townhouse",
            "description": "Well-maintained 3-bedroom townhouse in family-friendly neighborhood. Features include attached garage, private patio, and access to community amenities including playground and walking trails.",
            "address": "789 Maple Lane",
            "city": "Round Rock",
            "state": "TX",
            "zip_code": "78664",
            "latitude": 30.5083,
            "longitude": -97.6789,
            "price": 285000,
            "bedrooms": 3,
            "bathrooms": 2.5,
            "square_feet": 1800,
            "lot_size": 0.1,
            "year_built": 2010,
            "property_type": "townhouse",
            "listing_type": "for_sale"
        },
        {
            "title": "Spacious Ranch Style Home",
            "description": "Single-story ranch home on large lot. Perfect for those who prefer no stairs. Features include open floor plan, fireplace, and large master suite. Plenty of room for expansion.",
            "address": "321 Pine Avenue",
            "city": "Cedar Park",
            "state": "TX",
            "zip_code": "78613",
            "latitude": 30.5052,
            "longitude": -97.8203,
            "price": 375000,
            "bedrooms": 3,
            "bathrooms": 2.0,
            "square_feet": 2000,
            "lot_size": 0.5,
            "year_built": 2005,
            "property_type": "house",
            "listing_type": "for_sale"
        },
        {
            "title": "Modern Apartment for Rent",
            "description": "Brand new 1-bedroom apartment in trendy neighborhood. Features include stainless steel appliances, in-unit washer/dryer, and balcony with city views. Walking distance to restaurants and shops.",
            "address": "654 Congress Avenue, Unit 304",
            "city": "Austin",
            "state": "TX",
            "zip_code": "78701",
            "latitude": 30.2669,
            "longitude": -97.7428,
            "price": 1800,
            "bedrooms": 1,
            "bathrooms": 1.0,
            "square_feet": 750,
            "year_built": 2022,
            "property_type": "apartment",
            "listing_type": "for_rent"
        },
        {
            "title": "Executive Home with Pool",
            "description": "Impressive 5-bedroom executive home in prestigious neighborhood. Features include gourmet kitchen, formal dining room, home office, and resort-style backyard with pool and spa.",
            "address": "987 Highland Drive",
            "city": "Westlake",
            "state": "TX",
            "zip_code": "78746",
            "latitude": 30.2711,
            "longitude": -97.8494,
            "price": 750000,
            "bedrooms": 5,
            "bathrooms": 4.0,
            "square_feet": 3500,
            "lot_size": 0.75,
            "year_built": 2018,
            "property_type": "house",
            "listing_type": "for_sale"
        }
    ]
    
    created_properties = []
    for i, prop_data in enumerate(properties_data):
        agent = agents[i % len(agents)]  # Rotate through agents
        property_create = schemas.PropertyCreate(**prop_data)
        property = crud.create_property(db, property_create, agent.id)
        created_properties.append(property)
        print(f"Created property: {property.title}")
    
    return created_properties

def create_sample_images(db: Session, properties):
    """Create sample property images"""
    # Sample image URLs (using placeholder images)
    sample_images = [
        "https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop"
    ]
    
    for i, property in enumerate(properties):
        # Add 2-3 images per property
        num_images = min(3, len(sample_images))
        for j in range(num_images):
            image_data = schemas.PropertyImageCreate(
                image_url=sample_images[(i + j) % len(sample_images)],
                alt_text=f"{property.title} - Image {j + 1}",
                is_primary=(j == 0),
                order=j
            )
            crud.create_property_image(db, image_data, property.id)
        
        print(f"Added {num_images} images to property: {property.title}")

def main():
    """Main function to populate sample data"""
    db = SessionLocal()
    
    try:
        print("Creating sample data...")
        
        # Create users
        print("\n1. Creating sample users...")
        users = create_sample_users(db)
        agents = [user for user in users if user.is_agent]
        
        # Create properties
        print("\n2. Creating sample properties...")
        properties = create_sample_properties(db, agents)
        
        # Create property images
        print("\n3. Creating sample property images...")
        create_sample_images(db, properties)
        
        print(f"\nSample data creation completed!")
        print(f"Created {len(users)} users ({len(agents)} agents)")
        print(f"Created {len(properties)} properties")
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
