from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str
    first_name: str
    last_name: str
    phone: Optional[str] = None
    is_agent: bool = False

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

# Property Image schemas
class PropertyImageBase(BaseModel):
    image_url: str
    alt_text: Optional[str] = None
    is_primary: bool = False
    order: int = 0

class PropertyImageCreate(PropertyImageBase):
    pass

class PropertyImage(PropertyImageBase):
    id: int
    property_id: int
    
    class Config:
        from_attributes = True

# Property schemas
class PropertyBase(BaseModel):
    title: str
    description: Optional[str] = None
    address: str
    city: str
    state: str
    zip_code: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    price: int
    bedrooms: int
    bathrooms: float
    square_feet: Optional[int] = None
    lot_size: Optional[float] = None
    year_built: Optional[int] = None
    property_type: str
    listing_type: str = "for_sale"

class PropertyCreate(PropertyBase):
    pass

class Property(PropertyBase):
    id: int
    status: str
    listed_date: datetime
    updated_at: Optional[datetime] = None
    agent_id: Optional[int] = None
    agent: Optional[User] = None
    images: List[PropertyImage] = []
    
    class Config:
        from_attributes = True

# Search and filter schemas
class PropertySearch(BaseModel):
    location: Optional[str] = None
    min_price: Optional[int] = None
    max_price: Optional[int] = None
    bedrooms: Optional[int] = None
    bathrooms: Optional[float] = None
    property_type: Optional[str] = None
    listing_type: Optional[str] = "for_sale"
    sort_by: Optional[str] = "price_asc"  # price_asc, price_desc, date_desc, etc.
    page: int = 1
    limit: int = 20

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None
