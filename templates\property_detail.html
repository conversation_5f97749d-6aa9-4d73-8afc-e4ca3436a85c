{% extends "base.html" %}

{% block title %}{{ property.title }} - Real Home{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/search">Search</a></li>
            <li class="breadcrumb-item active">{{ property.city }}, {{ property.state }}</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- Property Images -->
        <div class="col-lg-8 mb-4">
            <div class="property-gallery">
                {% if property.images %}
                    <div class="mb-3">
                        <img src="{{ property.images[0].image_url }}" 
                             class="property-main-image rounded" 
                             alt="{{ property.title }}" 
                             id="mainImage">
                    </div>
                    
                    {% if property.images|length > 1 %}
                    <div class="row">
                        {% for image in property.images[:4] %}
                        <div class="col-3">
                            <img src="{{ image.image_url }}" 
                                 class="property-thumbnail rounded" 
                                 alt="{{ property.title }}"
                                 onclick="changeMainImage('{{ image.image_url }}')">
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                {% else %}
                    <div class="property-main-image bg-light d-flex align-items-center justify-content-center rounded">
                        <i class="fas fa-home fa-5x text-muted"></i>
                    </div>
                {% endif %}
            </div>
            
            <!-- Property Description -->
            <div class="mt-4">
                <h4>About This Property</h4>
                {% if property.description %}
                    <p class="text-muted">{{ property.description }}</p>
                {% else %}
                    <p class="text-muted">No description available for this property.</p>
                {% endif %}
            </div>
            
            <!-- Map -->
            {% if property.latitude and property.longitude %}
            <div class="mt-4">
                <h4>Location</h4>
                <div id="propertyMap" class="map-container"></div>
            </div>
            {% endif %}
        </div>
        
        <!-- Property Info Sidebar -->
        <div class="col-lg-4">
            <div class="property-info-card p-4 mb-4">
                <div class="property-price mb-3">${{ "{:,}".format(property.price) }}</div>
                
                <div class="property-features mb-4">
                    <div class="feature-item">
                        <i class="fas fa-bed"></i>
                        <span>{{ property.bedrooms }} Bedrooms</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-bath"></i>
                        <span>{{ property.bathrooms }} Bathrooms</span>
                    </div>
                    {% if property.square_feet %}
                    <div class="feature-item">
                        <i class="fas fa-ruler-combined"></i>
                        <span>{{ "{:,}".format(property.square_feet) }} sqft</span>
                    </div>
                    {% endif %}
                    {% if property.lot_size %}
                    <div class="feature-item">
                        <i class="fas fa-map"></i>
                        <span>{{ property.lot_size }} acres</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>Property Details</h6>
                    <table class="table table-sm">
                        <tr>
                            <td>Property Type:</td>
                            <td class="text-capitalize">{{ property.property_type }}</td>
                        </tr>
                        <tr>
                            <td>Listing Type:</td>
                            <td class="text-capitalize">{{ property.listing_type.replace('_', ' ') }}</td>
                        </tr>
                        {% if property.year_built %}
                        <tr>
                            <td>Year Built:</td>
                            <td>{{ property.year_built }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td>Status:</td>
                            <td class="text-capitalize">{{ property.status }}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="mb-4">
                    <h6>Address</h6>
                    <p class="mb-1">{{ property.address }}</p>
                    <p class="text-muted">{{ property.city }}, {{ property.state }} {{ property.zip_code }}</p>
                </div>
                
                {% if property.agent %}
                <div class="mb-4">
                    <h6>Listed By</h6>
                    <div class="d-flex align-items-center">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">{{ property.agent.first_name }} {{ property.agent.last_name }}</div>
                            {% if property.agent.phone %}
                            <small class="text-muted">{{ property.agent.phone }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="contactAgent()">
                        <i class="fas fa-phone me-2"></i>Contact Agent
                    </button>
                    <button class="btn btn-outline-primary" onclick="scheduleViewing()">
                        <i class="fas fa-calendar me-2"></i>Schedule Viewing
                    </button>
                    <button class="btn btn-outline-secondary" onclick="saveProperty()">
                        <i class="fas fa-heart me-2"></i>Save Property
                    </button>
                </div>
            </div>
            
            <!-- Similar Properties -->
            <div class="property-info-card p-4">
                <h6 class="mb-3">Similar Properties</h6>
                <p class="text-muted small">Find similar properties in {{ property.city }}, {{ property.state }}</p>
                <a href="/search?location={{ property.city }}, {{ property.state }}&property_type={{ property.property_type }}" 
                   class="btn btn-outline-primary btn-sm">
                    View Similar Properties
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Image gallery functionality
function changeMainImage(imageUrl) {
    document.getElementById('mainImage').src = imageUrl;
}

// Map functionality
{% if property.latitude and property.longitude %}
document.addEventListener('DOMContentLoaded', function() {
    const map = L.map('propertyMap').setView([{{ property.latitude }}, {{ property.longitude }}], 15);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    L.marker([{{ property.latitude }}, {{ property.longitude }}])
        .addTo(map)
        .bindPopup('{{ property.address }}<br>{{ property.city }}, {{ property.state }}')
        .openPopup();
});
{% endif %}

// Contact functionality (placeholder)
function contactAgent() {
    alert('Contact agent functionality would be implemented here.');
}

function scheduleViewing() {
    alert('Schedule viewing functionality would be implemented here.');
}

function saveProperty() {
    alert('Save property functionality would be implemented here.');
}
</script>
{% endblock %}
