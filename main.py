from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, Form, Query
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session
from typing import Optional
import models
import schemas
import crud
from database import engine, get_db
from routers import auth, properties

# Create database tables
models.Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(title="Real Home - Zillow Clone", version="1.0.0")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Include routers
app.include_router(auth.router)
app.include_router(properties.router)

# Frontend routes
@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    # Get featured properties for homepage
    featured_properties = crud.get_properties(db, skip=0, limit=6)
    return templates.TemplateResponse("index.html", {
        "request": request,
        "featured_properties": featured_properties
    })

@app.get("/search", response_class=HTMLResponse)
async def search_page(
    request: Request,
    location: Optional[str] = Query(None),
    min_price: Optional[int] = Query(None),
    max_price: Optional[int] = Query(None),
    bedrooms: Optional[int] = Query(None),
    bathrooms: Optional[float] = Query(None),
    property_type: Optional[str] = Query(None),
    sort_by: Optional[str] = Query("price_asc"),
    page: int = Query(1),
    db: Session = Depends(get_db)
):
    search_params = schemas.PropertySearch(
        location=location,
        min_price=min_price,
        max_price=max_price,
        bedrooms=bedrooms,
        bathrooms=bathrooms,
        property_type=property_type,
        sort_by=sort_by,
        page=page,
        limit=12
    )
    
    properties, total = crud.search_properties(db, search_params)
    total_pages = (total + search_params.limit - 1) // search_params.limit
    
    return templates.TemplateResponse("search.html", {
        "request": request,
        "properties": properties,
        "search_params": search_params,
        "total": total,
        "page": page,
        "total_pages": total_pages,
        "has_prev": page > 1,
        "has_next": page < total_pages,
        "prev_page": page - 1 if page > 1 else None,
        "next_page": page + 1 if page < total_pages else None
    })

@app.get("/property/{property_id}", response_class=HTMLResponse)
async def property_detail(request: Request, property_id: int, db: Session = Depends(get_db)):
    property = crud.get_property(db, property_id=property_id)
    if not property:
        raise HTTPException(status_code=404, detail="Property not found")
    
    return templates.TemplateResponse("property_detail.html", {
        "request": request,
        "property": property
    })

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/register", response_class=HTMLResponse)
async def register_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
