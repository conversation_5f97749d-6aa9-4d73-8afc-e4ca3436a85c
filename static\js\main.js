// Main JavaScript file for Real Home application

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
}

function formatNumber(number) {
    return new Intl.NumberFormat('en-US').format(number);
}

// Authentication utilities
function getAuthToken() {
    return localStorage.getItem('access_token');
}

function setAuthToken(token) {
    localStorage.setItem('access_token', token);
}

function removeAuthToken() {
    localStorage.removeItem('access_token');
}

function isAuthenticated() {
    return !!getAuthToken();
}

// API request helper
async function apiRequest(url, options = {}) {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(url, {
        ...options,
        headers
    });
    
    if (response.status === 401) {
        removeAuthToken();
        window.location.href = '/login';
        return;
    }
    
    return response;
}

// Search functionality
function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            // Add loading state
            const submitBtn = searchForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
                submitBtn.disabled = true;
            }
        });
    }
}

// Property card interactions
function initializePropertyCards() {
    const propertyCards = document.querySelectorAll('.property-card');
    
    propertyCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // Handle card clicks
        card.addEventListener('click', function(e) {
            if (e.target.tagName !== 'A' && e.target.tagName !== 'BUTTON') {
                const detailLink = this.querySelector('a[href*="/property/"]');
                if (detailLink) {
                    window.location.href = detailLink.href;
                }
            }
        });
    });
}

// Map functionality
function initializeMap(containerId, properties = []) {
    if (typeof L === 'undefined') {
        console.warn('Leaflet library not loaded');
        return;
    }
    
    const mapContainer = document.getElementById(containerId);
    if (!mapContainer) return;
    
    // Default center (can be customized)
    let center = [39.8283, -98.5795]; // Center of US
    let zoom = 4;
    
    if (properties.length > 0) {
        // Calculate center from properties
        const lats = properties.map(p => p.latitude).filter(lat => lat);
        const lngs = properties.map(p => p.longitude).filter(lng => lng);
        
        if (lats.length > 0 && lngs.length > 0) {
            center = [
                lats.reduce((a, b) => a + b) / lats.length,
                lngs.reduce((a, b) => a + b) / lngs.length
            ];
            zoom = 10;
        }
    }
    
    const map = L.map(containerId).setView(center, zoom);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Add property markers
    properties.forEach(property => {
        if (property.latitude && property.longitude) {
            const marker = L.marker([property.latitude, property.longitude])
                .addTo(map);
            
            const popupContent = `
                <div class="property-popup">
                    <h6>${property.title}</h6>
                    <p class="mb-1">${formatPrice(property.price)}</p>
                    <p class="mb-1 text-muted small">${property.address}</p>
                    <p class="mb-2 text-muted small">
                        ${property.bedrooms} bed • ${property.bathrooms} bath
                        ${property.square_feet ? ' • ' + formatNumber(property.square_feet) + ' sqft' : ''}
                    </p>
                    <a href="/property/${property.id}" class="btn btn-primary btn-sm">View Details</a>
                </div>
            `;
            
            marker.bindPopup(popupContent);
        }
    });
    
    return map;
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Initialize tooltips and popovers
function initializeBootstrapComponents() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Lazy loading for images
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Navbar scroll behavior
function initializeNavbar() {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    // Handle scroll behavior
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Handle active nav link
    const currentPath = window.location.pathname;
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });

    // Mobile menu close on link click
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeNavbar();
    initializeSearch();
    initializePropertyCards();
    initializeBootstrapComponents();
    initializeLazyLoading();

    // Update navigation based on authentication status
    updateNavigation();
});

// Update navigation based on authentication
function updateNavigation() {
    const authLinks = document.querySelectorAll('.auth-link');
    const userLinks = document.querySelectorAll('.user-link');
    
    if (isAuthenticated()) {
        authLinks.forEach(link => link.style.display = 'none');
        userLinks.forEach(link => link.style.display = 'block');
    } else {
        authLinks.forEach(link => link.style.display = 'block');
        userLinks.forEach(link => link.style.display = 'none');
    }
}

// Logout function
function logout() {
    removeAuthToken();
    window.location.href = '/';
}

// Export functions for global use
window.RealHome = {
    formatPrice,
    formatNumber,
    apiRequest,
    initializeMap,
    initializeNavbar,
    validateForm,
    logout
};
