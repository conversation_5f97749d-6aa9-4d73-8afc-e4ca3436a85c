# Real Home - Zillow Clone

A comprehensive real estate web application built with FastAPI and server-side rendering, replicating the functionality and design of Zillow.com.

## Features

### Backend (FastAPI)
- **REST API** with comprehensive property endpoints
- **User Authentication** with JWT tokens
- **Property Search** with advanced filtering (location, price, bedrooms, bathrooms, property type)
- **Database Models** for users, properties, and property images
- **SQLite Database** for development (easily configurable for PostgreSQL)
- **Password Hashing** with bcrypt
- **Data Validation** with Pydantic schemas

### Frontend (Server-Side Rendered)
- **Jinja2 Templates** for server-side rendering
- **Responsive Design** with Bootstrap 5
- **Interactive Maps** using Leaflet.js
- **Property Search** with filters and sorting
- **Property Detail Pages** with image galleries
- **User Authentication** pages (login/register)
- **Modern UI** inspired by Zillow's design

### Key Functionality
- **Property Listings** with images, prices, and details
- **Advanced Search** by location, price range, bedrooms, bathrooms, property type
- **Interactive Maps** showing property locations
- **Property Detail Views** with photo galleries and comprehensive information
- **User Registration** and authentication
- **Agent Profiles** and property management
- **Responsive Design** for mobile and desktop

## Project Structure

```
Real_Home/
├── main.py                 # FastAPI application entry point
├── models.py              # Database models (SQLAlchemy)
├── schemas.py             # Pydantic schemas for validation
├── database.py            # Database configuration
├── auth.py                # Authentication utilities
├── crud.py                # Database operations
├── sample_data.py         # Script to populate sample data
├── requirements.txt       # Python dependencies
├── routers/
│   ├── __init__.py
│   ├── auth.py           # Authentication routes
│   └── properties.py     # Property routes
├── templates/
│   ├── base.html         # Base template
│   ├── index.html        # Homepage
│   ├── search.html       # Search results page
│   ├── property_detail.html  # Property detail page
│   ├── login.html        # Login page
│   └── register.html     # Registration page
└── static/
    ├── css/
    │   └── style.css     # Custom CSS styles
    ├── js/
    │   └── main.js       # JavaScript functionality
    └── images/           # Static images
```

## Installation & Setup

1. **Clone or download the project files**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Create sample data:**
   ```bash
   python sample_data.py
   ```

4. **Run the application:**
   ```bash
   uvicorn main:app --reload --port 8000
   ```

5. **Access the application:**
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Sample Data

The application comes with sample data including:
- **3 Users** (2 real estate agents, 1 buyer)
- **6 Properties** with various types (houses, condos, townhouses, apartments)
- **Property Images** from Unsplash
- **Locations** in Austin, TX area

### Sample Login Credentials
- **Agent 1:** Username: `johnagent`, Password: `password123`
- **Agent 2:** Username: `sarahagent`, Password: `password123`
- **Buyer:** Username: `mikebuyer`, Password: `password123`

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user info

### Properties
- `GET /api/properties` - Get all properties
- `GET /api/properties/search` - Search properties with filters
- `GET /api/properties/{id}` - Get property details
- `POST /api/properties` - Create new property (authenticated)
- `POST /api/properties/{id}/images` - Add property images (authenticated)

### Frontend Routes
- `GET /` - Homepage with featured properties
- `GET /search` - Property search page
- `GET /property/{id}` - Property detail page
- `GET /login` - Login page
- `GET /register` - Registration page

## Technology Stack

- **Backend:** FastAPI, SQLAlchemy, SQLite
- **Frontend:** Jinja2, Bootstrap 5, Vanilla JavaScript
- **Maps:** Leaflet.js with OpenStreetMap
- **Authentication:** JWT tokens with bcrypt password hashing
- **Styling:** Custom CSS with Bootstrap components
- **Images:** Unsplash API for sample property photos

## Features Implemented

✅ Property search with multiple filters  
✅ Interactive maps with property markers  
✅ Responsive design for mobile/desktop  
✅ User authentication and registration  
✅ Property detail pages with image galleries  
✅ Agent profiles and property management  
✅ Modern, Zillow-inspired UI design  
✅ RESTful API with comprehensive endpoints  
✅ Database relationships and data validation  
✅ Server-side rendering with Jinja2  

## Future Enhancements

- Property favorites/saved searches
- Advanced map clustering
- Property comparison tool
- Email notifications
- Payment integration
- Advanced search filters (school districts, amenities)
- Property history and price trends
- Virtual tours integration

## Development Notes

- The application uses SQLite for development but can be easily configured for PostgreSQL
- All passwords are hashed using bcrypt
- JWT tokens are used for authentication
- The frontend uses server-side rendering for better SEO and initial load performance
- Bootstrap 5 provides responsive design and modern UI components
- Leaflet.js provides interactive maps without requiring API keys

## License

This project is for educational and demonstration purposes.
